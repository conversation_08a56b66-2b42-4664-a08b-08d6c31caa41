{{- if .Values.serviceAccounts.nodeplugin.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "ceph-csi-rbd.serviceAccountName.nodeplugin" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ include "ceph-csi-rbd.name" . }}
    chart: {{ include "ceph-csi-rbd.chart" . }}
    component: {{ .Values.nodeplugin.name }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- end -}}
