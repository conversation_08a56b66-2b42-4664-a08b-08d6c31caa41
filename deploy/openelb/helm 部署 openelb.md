
# 

## 复制 chargs 到 k8s 主节点
```shell
scp -r ./charts root@<k8s-master-ip>:/root/
```

## 本地安装
```shell
sudo helm install openelb ./chargs -n openelb-system --create-namespace
```

### 使用VIP模式
#### 1.启用 VIP 模式
VIP 模式可以通过命令行参数来启用或禁用。在使用 VIP 模式时，请确保 VIP 扬声器已正确启用。

运行以下命令编辑 openelb-speaker DaemonSet：
```shell
kubectl edit ds -n openelb-system openelb-speaker
```
将 enable-keepalived-vip 设置为 true 并保存更改。openelb-speaker 将自动重启。

```yaml
   containers:
     - command:
         - openelb-speaker
       args:
         - --api-hosts=:50051
         - --enable-keepalived-vip=true
```
### 2.创建 Eip 对象
Eip 对象作为 OpenELB 的 IP 地址池使用。
```yaml
apiVersion: network.kubesphere.io/v1alpha2
kind: Eip
metadata:
  name: vip-eip
spec:
  address: ************-*************
  interface: eth0
  protocol: vip
```
> spec:address 中指定的 IP 地址必须与 Kubernetes 集群节点位于同一网络段。

> 有关 Eip YAML 配置字段的详细信息，请参阅使用 Eip 配置 IP 地址池(https://openelb.io/docs/getting-started/configuration/configure-ip-address-pools-using-eip/) 。

### 3.创建 LoadBalancerService 对象

```yaml
kind: Service
apiVersion: v1
metadata:
  name: vip-svc
  annotations:
    lb.kubesphere.io/v1alpha1: openelb
    # For versions below 0.6.0, you also need to specify the protocol
    # protocol.openelb.kubesphere.io/v1alpha1: vip
    eip.openelb.kubesphere.io/v1alpha2: vip-eip
spec:
  selector:
    app: vip-openelb
  type: LoadBalancer
  ports:
    - name: http
      port: 80
      targetPort: 8080
  externalTrafficPolicy: Cluster
```
kubeSphere 中使用集群网关可掠过此步
```yaml
# 注意: 集群网关配置的 annotations 同上
apiVersion: networking.kubesphere.io/v1alpha1
kind: Gateway
metadata:
  name: vip-gateway
  annotations:
    lb.kubesphere.io/v1alpha1: openelb
    # For versions below 0.6.0, you also need to specify the protocol
    # protocol.openelb.kubesphere.io/v1alpha1: vip
    eip.openelb.kubesphere.io/v1alpha2: vip-eip
  

```

## 卸载
```shell
sudo helm delete openelb -n openelb-system
```


