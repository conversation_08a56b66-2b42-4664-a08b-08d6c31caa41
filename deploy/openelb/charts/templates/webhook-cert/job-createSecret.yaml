apiVersion: batch/v1
kind: Job
metadata:
  labels:
  {{- include "openelb.admission.labels" . | nindent 4 }}
  name: {{ template "openelb.admission.fullname" . }}-create
  namespace: {{ template "openelb.namespace" . }}
spec:
  template:
    metadata:
      labels:
        {{- include "openelb.admission.labels" . | nindent 8 }}
      name: {{ template "openelb.admission.fullname" . }}-create
    spec:
      containers:
        - args:
            - create
            - --host={{ template "openelb.controller.fullname" . }},{{ template "openelb.controller.fullname" . }}.$(POD_NAMESPACE).svc
            - --namespace=$(POD_NAMESPACE)
            - --secret-name={{ template "openelb.admission.fullname" . }}
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          image: {{ template "admission.image" . }}
          imagePullPolicy: {{ .Values.admission.image.pullPolicy }}
          name: create
      restartPolicy: OnFailure
      securityContext:
        runAsNonRoot: true
        runAsUser: 2000
      serviceAccountName: {{ template "openelb.admission.serviceAccountName" . }}