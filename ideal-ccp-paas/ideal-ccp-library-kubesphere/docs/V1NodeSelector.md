

# V1NodeSelector

A node selector represents the union of the results of one or more label queries over a set of nodes; that is, it represents the OR of the selectors represented by the node selector terms.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**nodeSelectorTerms** | [**List&lt;V1NodeSelectorTerm&gt;**](V1NodeSelectorTerm.md) | Required. A list of node selector terms. The terms are ORed. |  |



