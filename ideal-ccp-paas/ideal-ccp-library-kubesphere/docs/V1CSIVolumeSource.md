

# V1CSIVolumeSource

Represents a source location of a volume to mount, managed by an external CSI driver

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**driver** | **String** | driver is the name of the CSI driver that handles this volume. Consult with your admin for the correct name as registered in the cluster. |  |
|**fsType** | **String** | fsType to mount. Ex. \&quot;ext4\&quot;, \&quot;xfs\&quot;, \&quot;ntfs\&quot;. If not provided, the empty value is passed to the associated CSI driver which will determine the default filesystem to apply. |  [optional] |
|**nodePublishSecretRef** | [**V1LocalObjectReference**](V1LocalObjectReference.md) |  |  [optional] |
|**readOnly** | **Boolean** | readOnly specifies a read-only configuration for the volume. Defaults to false (read/write). |  [optional] |
|**volumeAttributes** | **Map&lt;String, String&gt;** | volumeAttributes stores driver-specific properties that are passed to the CSI driver. Consult your driver&#39;s documentation for supported values. |  [optional] |



