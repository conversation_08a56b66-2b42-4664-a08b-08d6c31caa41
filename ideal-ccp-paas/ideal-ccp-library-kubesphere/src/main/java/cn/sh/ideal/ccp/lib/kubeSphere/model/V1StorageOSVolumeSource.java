/*
 * KS API
 * KubeSphere OpenAPI
 *
 * The version of the OpenAPI document: v4.1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package cn.sh.ideal.ccp.lib.kubeSphere.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * Represents a StorageOS persistent volume resource.
 */
@JsonPropertyOrder({
  V1StorageOSVolumeSource.JSON_PROPERTY_FS_TYPE,
  V1StorageOSVolumeSource.JSON_PROPERTY_READ_ONLY,
  V1StorageOSVolumeSource.JSON_PROPERTY_SECRET_REF,
  V1StorageOSVolumeSource.JSON_PROPERTY_VOLUME_NAME,
  V1StorageOSVolumeSource.JSON_PROPERTY_VOLUME_NAMESPACE
})
@JsonTypeName("v1.StorageOSVolumeSource")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-07T14:59:55.982671800+08:00[GMT+08:00]", comments = "Generator version: 7.13.0")
public class V1StorageOSVolumeSource {
  public static final String JSON_PROPERTY_FS_TYPE = "fsType";
  @javax.annotation.Nullable
  private String fsType;

  public static final String JSON_PROPERTY_READ_ONLY = "readOnly";
  @javax.annotation.Nullable
  private Boolean readOnly;

  public static final String JSON_PROPERTY_SECRET_REF = "secretRef";
  @javax.annotation.Nullable
  private V1LocalObjectReference secretRef;

  public static final String JSON_PROPERTY_VOLUME_NAME = "volumeName";
  @javax.annotation.Nullable
  private String volumeName;

  public static final String JSON_PROPERTY_VOLUME_NAMESPACE = "volumeNamespace";
  @javax.annotation.Nullable
  private String volumeNamespace;

  public V1StorageOSVolumeSource() {
  }

  public V1StorageOSVolumeSource fsType(@javax.annotation.Nullable String fsType) {

    this.fsType = fsType;
    return this;
  }

  /**
   * fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \&quot;ext4\&quot;, \&quot;xfs\&quot;, \&quot;ntfs\&quot;. Implicitly inferred to be \&quot;ext4\&quot; if unspecified.
   * @return fsType
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FS_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFsType() {
    return fsType;
  }


  @JsonProperty(JSON_PROPERTY_FS_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFsType(@javax.annotation.Nullable String fsType) {
    this.fsType = fsType;
  }

  public V1StorageOSVolumeSource readOnly(@javax.annotation.Nullable Boolean readOnly) {

    this.readOnly = readOnly;
    return this;
  }

  /**
   * readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.
   * @return readOnly
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_READ_ONLY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReadOnly() {
    return readOnly;
  }


  @JsonProperty(JSON_PROPERTY_READ_ONLY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReadOnly(@javax.annotation.Nullable Boolean readOnly) {
    this.readOnly = readOnly;
  }

  public V1StorageOSVolumeSource secretRef(@javax.annotation.Nullable V1LocalObjectReference secretRef) {

    this.secretRef = secretRef;
    return this;
  }

  /**
   * Get secretRef
   * @return secretRef
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SECRET_REF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V1LocalObjectReference getSecretRef() {
    return secretRef;
  }


  @JsonProperty(JSON_PROPERTY_SECRET_REF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSecretRef(@javax.annotation.Nullable V1LocalObjectReference secretRef) {
    this.secretRef = secretRef;
  }

  public V1StorageOSVolumeSource volumeName(@javax.annotation.Nullable String volumeName) {

    this.volumeName = volumeName;
    return this;
  }

  /**
   * volumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.
   * @return volumeName
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VOLUME_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVolumeName() {
    return volumeName;
  }


  @JsonProperty(JSON_PROPERTY_VOLUME_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVolumeName(@javax.annotation.Nullable String volumeName) {
    this.volumeName = volumeName;
  }

  public V1StorageOSVolumeSource volumeNamespace(@javax.annotation.Nullable String volumeNamespace) {

    this.volumeNamespace = volumeNamespace;
    return this;
  }

  /**
   * volumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod&#39;s namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \&quot;default\&quot; if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.
   * @return volumeNamespace
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VOLUME_NAMESPACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVolumeNamespace() {
    return volumeNamespace;
  }


  @JsonProperty(JSON_PROPERTY_VOLUME_NAMESPACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVolumeNamespace(@javax.annotation.Nullable String volumeNamespace) {
    this.volumeNamespace = volumeNamespace;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    V1StorageOSVolumeSource v1StorageOSVolumeSource = (V1StorageOSVolumeSource) o;
    return Objects.equals(this.fsType, v1StorageOSVolumeSource.fsType) &&
        Objects.equals(this.readOnly, v1StorageOSVolumeSource.readOnly) &&
        Objects.equals(this.secretRef, v1StorageOSVolumeSource.secretRef) &&
        Objects.equals(this.volumeName, v1StorageOSVolumeSource.volumeName) &&
        Objects.equals(this.volumeNamespace, v1StorageOSVolumeSource.volumeNamespace);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fsType, readOnly, secretRef, volumeName, volumeNamespace);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class V1StorageOSVolumeSource {\n");
    sb.append("    fsType: ").append(toIndentedString(fsType)).append("\n");
    sb.append("    readOnly: ").append(toIndentedString(readOnly)).append("\n");
    sb.append("    secretRef: ").append(toIndentedString(secretRef)).append("\n");
    sb.append("    volumeName: ").append(toIndentedString(volumeName)).append("\n");
    sb.append("    volumeNamespace: ").append(toIndentedString(volumeNamespace)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

