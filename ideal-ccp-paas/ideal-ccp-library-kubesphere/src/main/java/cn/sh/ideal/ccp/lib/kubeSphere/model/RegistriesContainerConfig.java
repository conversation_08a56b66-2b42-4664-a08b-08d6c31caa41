/*
 * KS API
 * KubeSphere OpenAPI
 *
 * The version of the OpenAPI document: v4.1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package cn.sh.ideal.ccp.lib.kubeSphere.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * RegistriesContainerConfig
 */
@JsonPropertyOrder({
  RegistriesContainerConfig.JSON_PROPERTY_ARGS_ESCAPED,
  RegistriesContainerConfig.JSON_PROPERTY_ATTACH_STDERR,
  RegistriesContainerConfig.JSON_PROPERTY_ATTACH_STDIN,
  RegistriesContainerConfig.JSON_PROPERTY_ATTACH_STDOUT,
  RegistriesContainerConfig.JSON_PROPERTY_CMD,
  RegistriesContainerConfig.JSON_PROPERTY_DOMAINNAME,
  RegistriesContainerConfig.JSON_PROPERTY_ENTRYPOINT,
  RegistriesContainerConfig.JSON_PROPERTY_ENV,
  RegistriesContainerConfig.JSON_PROPERTY_EXPOSED_PORTS,
  RegistriesContainerConfig.JSON_PROPERTY_HOSTNAME,
  RegistriesContainerConfig.JSON_PROPERTY_IMAGE,
  RegistriesContainerConfig.JSON_PROPERTY_LABELS,
  RegistriesContainerConfig.JSON_PROPERTY_ON_BUILD,
  RegistriesContainerConfig.JSON_PROPERTY_OPEN_STDIN,
  RegistriesContainerConfig.JSON_PROPERTY_STDIN_ONCE,
  RegistriesContainerConfig.JSON_PROPERTY_STOP_SIGNAL,
  RegistriesContainerConfig.JSON_PROPERTY_TTY,
  RegistriesContainerConfig.JSON_PROPERTY_USER,
  RegistriesContainerConfig.JSON_PROPERTY_VOLUMES,
  RegistriesContainerConfig.JSON_PROPERTY_WORKING_DIR
})
@JsonTypeName("registries.ContainerConfig")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-07T14:59:55.982671800+08:00[GMT+08:00]", comments = "Generator version: 7.13.0")
public class RegistriesContainerConfig {
  public static final String JSON_PROPERTY_ARGS_ESCAPED = "ArgsEscaped";
  @javax.annotation.Nullable
  private Boolean argsEscaped;

  public static final String JSON_PROPERTY_ATTACH_STDERR = "AttachStderr";
  @javax.annotation.Nullable
  private Boolean attachStderr;

  public static final String JSON_PROPERTY_ATTACH_STDIN = "AttachStdin";
  @javax.annotation.Nullable
  private Boolean attachStdin;

  public static final String JSON_PROPERTY_ATTACH_STDOUT = "AttachStdout";
  @javax.annotation.Nullable
  private Boolean attachStdout;

  public static final String JSON_PROPERTY_CMD = "Cmd";
  @javax.annotation.Nullable
  private List<String> cmd = new ArrayList<>();

  public static final String JSON_PROPERTY_DOMAINNAME = "Domainname";
  @javax.annotation.Nullable
  private String domainname;

  public static final String JSON_PROPERTY_ENTRYPOINT = "Entrypoint";
  @javax.annotation.Nullable
  private Object entrypoint;

  public static final String JSON_PROPERTY_ENV = "Env";
  @javax.annotation.Nullable
  private List<String> env = new ArrayList<>();

  public static final String JSON_PROPERTY_EXPOSED_PORTS = "ExposedPorts";
  @javax.annotation.Nullable
  private Object exposedPorts;

  public static final String JSON_PROPERTY_HOSTNAME = "Hostname";
  @javax.annotation.Nullable
  private String hostname;

  public static final String JSON_PROPERTY_IMAGE = "Image";
  @javax.annotation.Nullable
  private String image;

  public static final String JSON_PROPERTY_LABELS = "Labels";
  @javax.annotation.Nullable
  private RegistriesLabels labels;

  public static final String JSON_PROPERTY_ON_BUILD = "OnBuild";
  @javax.annotation.Nullable
  private Object onBuild;

  public static final String JSON_PROPERTY_OPEN_STDIN = "OpenStdin";
  @javax.annotation.Nullable
  private Boolean openStdin;

  public static final String JSON_PROPERTY_STDIN_ONCE = "StdinOnce";
  @javax.annotation.Nullable
  private Boolean stdinOnce;

  public static final String JSON_PROPERTY_STOP_SIGNAL = "StopSignal";
  @javax.annotation.Nullable
  private String stopSignal;

  public static final String JSON_PROPERTY_TTY = "Tty";
  @javax.annotation.Nullable
  private Boolean tty;

  public static final String JSON_PROPERTY_USER = "User";
  @javax.annotation.Nullable
  private String user;

  public static final String JSON_PROPERTY_VOLUMES = "Volumes";
  @javax.annotation.Nullable
  private Object volumes;

  public static final String JSON_PROPERTY_WORKING_DIR = "WorkingDir";
  @javax.annotation.Nullable
  private String workingDir;

  public RegistriesContainerConfig() {
  }

  public RegistriesContainerConfig argsEscaped(@javax.annotation.Nullable Boolean argsEscaped) {

    this.argsEscaped = argsEscaped;
    return this;
  }

  /**
   * Command is already escaped (Windows only)
   * @return argsEscaped
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ARGS_ESCAPED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getArgsEscaped() {
    return argsEscaped;
  }


  @JsonProperty(JSON_PROPERTY_ARGS_ESCAPED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setArgsEscaped(@javax.annotation.Nullable Boolean argsEscaped) {
    this.argsEscaped = argsEscaped;
  }

  public RegistriesContainerConfig attachStderr(@javax.annotation.Nullable Boolean attachStderr) {

    this.attachStderr = attachStderr;
    return this;
  }

  /**
   * Boolean value, attaches to stderr.
   * @return attachStderr
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ATTACH_STDERR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAttachStderr() {
    return attachStderr;
  }


  @JsonProperty(JSON_PROPERTY_ATTACH_STDERR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAttachStderr(@javax.annotation.Nullable Boolean attachStderr) {
    this.attachStderr = attachStderr;
  }

  public RegistriesContainerConfig attachStdin(@javax.annotation.Nullable Boolean attachStdin) {

    this.attachStdin = attachStdin;
    return this;
  }

  /**
   * Boolean value, attaches to stdin.
   * @return attachStdin
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ATTACH_STDIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAttachStdin() {
    return attachStdin;
  }


  @JsonProperty(JSON_PROPERTY_ATTACH_STDIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAttachStdin(@javax.annotation.Nullable Boolean attachStdin) {
    this.attachStdin = attachStdin;
  }

  public RegistriesContainerConfig attachStdout(@javax.annotation.Nullable Boolean attachStdout) {

    this.attachStdout = attachStdout;
    return this;
  }

  /**
   * Boolean value, attaches to stdout.
   * @return attachStdout
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ATTACH_STDOUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAttachStdout() {
    return attachStdout;
  }


  @JsonProperty(JSON_PROPERTY_ATTACH_STDOUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAttachStdout(@javax.annotation.Nullable Boolean attachStdout) {
    this.attachStdout = attachStdout;
  }

  public RegistriesContainerConfig cmd(@javax.annotation.Nullable List<String> cmd) {

    this.cmd = cmd;
    return this;
  }

  public RegistriesContainerConfig addCmdItem(String cmdItem) {
    if (this.cmd == null) {
      this.cmd = new ArrayList<>();
    }
    this.cmd.add(cmdItem);
    return this;
  }

  /**
   * Command to run specified as a string or an array of strings.
   * @return cmd
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CMD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getCmd() {
    return cmd;
  }


  @JsonProperty(JSON_PROPERTY_CMD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCmd(@javax.annotation.Nullable List<String> cmd) {
    this.cmd = cmd;
  }

  public RegistriesContainerConfig domainname(@javax.annotation.Nullable String domainname) {

    this.domainname = domainname;
    return this;
  }

  /**
   * A string value containing the domain name to use for the container.
   * @return domainname
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOMAINNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDomainname() {
    return domainname;
  }


  @JsonProperty(JSON_PROPERTY_DOMAINNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDomainname(@javax.annotation.Nullable String domainname) {
    this.domainname = domainname;
  }

  public RegistriesContainerConfig entrypoint(@javax.annotation.Nullable Object entrypoint) {

    this.entrypoint = entrypoint;
    return this;
  }

  /**
   * Get entrypoint
   * @return entrypoint
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTRYPOINT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getEntrypoint() {
    return entrypoint;
  }


  @JsonProperty(JSON_PROPERTY_ENTRYPOINT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntrypoint(@javax.annotation.Nullable Object entrypoint) {
    this.entrypoint = entrypoint;
  }

  public RegistriesContainerConfig env(@javax.annotation.Nullable List<String> env) {

    this.env = env;
    return this;
  }

  public RegistriesContainerConfig addEnvItem(String envItem) {
    if (this.env == null) {
      this.env = new ArrayList<>();
    }
    this.env.add(envItem);
    return this;
  }

  /**
   * A list of environment variables in the form of [\&quot;VAR&#x3D;value\&quot;, ...]
   * @return env
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENV)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getEnv() {
    return env;
  }


  @JsonProperty(JSON_PROPERTY_ENV)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEnv(@javax.annotation.Nullable List<String> env) {
    this.env = env;
  }

  public RegistriesContainerConfig exposedPorts(@javax.annotation.Nullable Object exposedPorts) {

    this.exposedPorts = exposedPorts;
    return this;
  }

  /**
   * An object mapping ports to an empty object in the form of: \&quot;ExposedPorts\&quot;: { \&quot;&lt;port&gt;/&lt;tcp|udp&gt;: {}\&quot; }
   * @return exposedPorts
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXPOSED_PORTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getExposedPorts() {
    return exposedPorts;
  }


  @JsonProperty(JSON_PROPERTY_EXPOSED_PORTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExposedPorts(@javax.annotation.Nullable Object exposedPorts) {
    this.exposedPorts = exposedPorts;
  }

  public RegistriesContainerConfig hostname(@javax.annotation.Nullable String hostname) {

    this.hostname = hostname;
    return this;
  }

  /**
   * A string value containing the hostname to use for the container.
   * @return hostname
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHostname() {
    return hostname;
  }


  @JsonProperty(JSON_PROPERTY_HOSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHostname(@javax.annotation.Nullable String hostname) {
    this.hostname = hostname;
  }

  public RegistriesContainerConfig image(@javax.annotation.Nullable String image) {

    this.image = image;
    return this;
  }

  /**
   * A string specifying the image name to use for the container.
   * @return image
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IMAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getImage() {
    return image;
  }


  @JsonProperty(JSON_PROPERTY_IMAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setImage(@javax.annotation.Nullable String image) {
    this.image = image;
  }

  public RegistriesContainerConfig labels(@javax.annotation.Nullable RegistriesLabels labels) {

    this.labels = labels;
    return this;
  }

  /**
   * Get labels
   * @return labels
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LABELS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public RegistriesLabels getLabels() {
    return labels;
  }


  @JsonProperty(JSON_PROPERTY_LABELS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLabels(@javax.annotation.Nullable RegistriesLabels labels) {
    this.labels = labels;
  }

  public RegistriesContainerConfig onBuild(@javax.annotation.Nullable Object onBuild) {

    this.onBuild = onBuild;
    return this;
  }

  /**
   * Get onBuild
   * @return onBuild
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ON_BUILD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getOnBuild() {
    return onBuild;
  }


  @JsonProperty(JSON_PROPERTY_ON_BUILD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOnBuild(@javax.annotation.Nullable Object onBuild) {
    this.onBuild = onBuild;
  }

  public RegistriesContainerConfig openStdin(@javax.annotation.Nullable Boolean openStdin) {

    this.openStdin = openStdin;
    return this;
  }

  /**
   * Boolean value, opens stdin
   * @return openStdin
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPEN_STDIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOpenStdin() {
    return openStdin;
  }


  @JsonProperty(JSON_PROPERTY_OPEN_STDIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOpenStdin(@javax.annotation.Nullable Boolean openStdin) {
    this.openStdin = openStdin;
  }

  public RegistriesContainerConfig stdinOnce(@javax.annotation.Nullable Boolean stdinOnce) {

    this.stdinOnce = stdinOnce;
    return this;
  }

  /**
   * Boolean value, close stdin after the 1 attached client disconnects.
   * @return stdinOnce
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STDIN_ONCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getStdinOnce() {
    return stdinOnce;
  }


  @JsonProperty(JSON_PROPERTY_STDIN_ONCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStdinOnce(@javax.annotation.Nullable Boolean stdinOnce) {
    this.stdinOnce = stdinOnce;
  }

  public RegistriesContainerConfig stopSignal(@javax.annotation.Nullable String stopSignal) {

    this.stopSignal = stopSignal;
    return this;
  }

  /**
   * Signal to stop a container as a string or unsigned integer.
   * @return stopSignal
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STOP_SIGNAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStopSignal() {
    return stopSignal;
  }


  @JsonProperty(JSON_PROPERTY_STOP_SIGNAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStopSignal(@javax.annotation.Nullable String stopSignal) {
    this.stopSignal = stopSignal;
  }

  public RegistriesContainerConfig tty(@javax.annotation.Nullable Boolean tty) {

    this.tty = tty;
    return this;
  }

  /**
   * Boolean value, Attach standard streams to a tty, including stdin if it is not closed.
   * @return tty
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTty() {
    return tty;
  }


  @JsonProperty(JSON_PROPERTY_TTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTty(@javax.annotation.Nullable Boolean tty) {
    this.tty = tty;
  }

  public RegistriesContainerConfig user(@javax.annotation.Nullable String user) {

    this.user = user;
    return this;
  }

  /**
   * A string value specifying the user inside the container.
   * @return user
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUser() {
    return user;
  }


  @JsonProperty(JSON_PROPERTY_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUser(@javax.annotation.Nullable String user) {
    this.user = user;
  }

  public RegistriesContainerConfig volumes(@javax.annotation.Nullable Object volumes) {

    this.volumes = volumes;
    return this;
  }

  /**
   * Get volumes
   * @return volumes
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VOLUMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getVolumes() {
    return volumes;
  }


  @JsonProperty(JSON_PROPERTY_VOLUMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVolumes(@javax.annotation.Nullable Object volumes) {
    this.volumes = volumes;
  }

  public RegistriesContainerConfig workingDir(@javax.annotation.Nullable String workingDir) {

    this.workingDir = workingDir;
    return this;
  }

  /**
   * A string specifying the working directory for commands to run in.
   * @return workingDir
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKING_DIR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkingDir() {
    return workingDir;
  }


  @JsonProperty(JSON_PROPERTY_WORKING_DIR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkingDir(@javax.annotation.Nullable String workingDir) {
    this.workingDir = workingDir;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RegistriesContainerConfig registriesContainerConfig = (RegistriesContainerConfig) o;
    return Objects.equals(this.argsEscaped, registriesContainerConfig.argsEscaped) &&
        Objects.equals(this.attachStderr, registriesContainerConfig.attachStderr) &&
        Objects.equals(this.attachStdin, registriesContainerConfig.attachStdin) &&
        Objects.equals(this.attachStdout, registriesContainerConfig.attachStdout) &&
        Objects.equals(this.cmd, registriesContainerConfig.cmd) &&
        Objects.equals(this.domainname, registriesContainerConfig.domainname) &&
        Objects.equals(this.entrypoint, registriesContainerConfig.entrypoint) &&
        Objects.equals(this.env, registriesContainerConfig.env) &&
        Objects.equals(this.exposedPorts, registriesContainerConfig.exposedPorts) &&
        Objects.equals(this.hostname, registriesContainerConfig.hostname) &&
        Objects.equals(this.image, registriesContainerConfig.image) &&
        Objects.equals(this.labels, registriesContainerConfig.labels) &&
        Objects.equals(this.onBuild, registriesContainerConfig.onBuild) &&
        Objects.equals(this.openStdin, registriesContainerConfig.openStdin) &&
        Objects.equals(this.stdinOnce, registriesContainerConfig.stdinOnce) &&
        Objects.equals(this.stopSignal, registriesContainerConfig.stopSignal) &&
        Objects.equals(this.tty, registriesContainerConfig.tty) &&
        Objects.equals(this.user, registriesContainerConfig.user) &&
        Objects.equals(this.volumes, registriesContainerConfig.volumes) &&
        Objects.equals(this.workingDir, registriesContainerConfig.workingDir);
  }

  @Override
  public int hashCode() {
    return Objects.hash(argsEscaped, attachStderr, attachStdin, attachStdout, cmd, domainname, entrypoint, env, exposedPorts, hostname, image, labels, onBuild, openStdin, stdinOnce, stopSignal, tty, user, volumes, workingDir);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RegistriesContainerConfig {\n");
    sb.append("    argsEscaped: ").append(toIndentedString(argsEscaped)).append("\n");
    sb.append("    attachStderr: ").append(toIndentedString(attachStderr)).append("\n");
    sb.append("    attachStdin: ").append(toIndentedString(attachStdin)).append("\n");
    sb.append("    attachStdout: ").append(toIndentedString(attachStdout)).append("\n");
    sb.append("    cmd: ").append(toIndentedString(cmd)).append("\n");
    sb.append("    domainname: ").append(toIndentedString(domainname)).append("\n");
    sb.append("    entrypoint: ").append(toIndentedString(entrypoint)).append("\n");
    sb.append("    env: ").append(toIndentedString(env)).append("\n");
    sb.append("    exposedPorts: ").append(toIndentedString(exposedPorts)).append("\n");
    sb.append("    hostname: ").append(toIndentedString(hostname)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    labels: ").append(toIndentedString(labels)).append("\n");
    sb.append("    onBuild: ").append(toIndentedString(onBuild)).append("\n");
    sb.append("    openStdin: ").append(toIndentedString(openStdin)).append("\n");
    sb.append("    stdinOnce: ").append(toIndentedString(stdinOnce)).append("\n");
    sb.append("    stopSignal: ").append(toIndentedString(stopSignal)).append("\n");
    sb.append("    tty: ").append(toIndentedString(tty)).append("\n");
    sb.append("    user: ").append(toIndentedString(user)).append("\n");
    sb.append("    volumes: ").append(toIndentedString(volumes)).append("\n");
    sb.append("    workingDir: ").append(toIndentedString(workingDir)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

