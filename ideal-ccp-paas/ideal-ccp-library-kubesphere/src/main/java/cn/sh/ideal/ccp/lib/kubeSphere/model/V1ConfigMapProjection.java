/*
 * KS API
 * KubeSphere OpenAPI
 *
 * The version of the OpenAPI document: v4.1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package cn.sh.ideal.ccp.lib.kubeSphere.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Adapts a ConfigMap into a projected volume.  The contents of the target ConfigMap&#39;s Data field will be presented in a projected volume as files using the keys in the Data field as the file names, unless the items element is populated with specific mappings of keys to paths. Note that this is identical to a configmap volume source without the default mode.
 */
@JsonPropertyOrder({
  V1ConfigMapProjection.JSON_PROPERTY_ITEMS,
  V1ConfigMapProjection.JSON_PROPERTY_NAME,
  V1ConfigMapProjection.JSON_PROPERTY_OPTIONAL
})
@JsonTypeName("v1.ConfigMapProjection")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-07T14:59:55.982671800+08:00[GMT+08:00]", comments = "Generator version: 7.13.0")
public class V1ConfigMapProjection {
  public static final String JSON_PROPERTY_ITEMS = "items";
  @javax.annotation.Nullable
  private List<V1KeyToPath> items = new ArrayList<>();

  public static final String JSON_PROPERTY_NAME = "name";
  @javax.annotation.Nullable
  private String name;

  public static final String JSON_PROPERTY_OPTIONAL = "optional";
  @javax.annotation.Nullable
  private Boolean optional;

  public V1ConfigMapProjection() {
  }

  public V1ConfigMapProjection items(@javax.annotation.Nullable List<V1KeyToPath> items) {

    this.items = items;
    return this;
  }

  public V1ConfigMapProjection addItemsItem(V1KeyToPath itemsItem) {
    if (this.items == null) {
      this.items = new ArrayList<>();
    }
    this.items.add(itemsItem);
    return this;
  }

  /**
   * items if unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the &#39;..&#39; path or start with &#39;..&#39;.
   * @return items
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ITEMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<V1KeyToPath> getItems() {
    return items;
  }


  @JsonProperty(JSON_PROPERTY_ITEMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setItems(@javax.annotation.Nullable List<V1KeyToPath> items) {
    this.items = items;
  }

  public V1ConfigMapProjection name(@javax.annotation.Nullable String name) {

    this.name = name;
    return this;
  }

  /**
   * Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
   * @return name
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getName() {
    return name;
  }


  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setName(@javax.annotation.Nullable String name) {
    this.name = name;
  }

  public V1ConfigMapProjection optional(@javax.annotation.Nullable Boolean optional) {

    this.optional = optional;
    return this;
  }

  /**
   * optional specify whether the ConfigMap or its keys must be defined
   * @return optional
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPTIONAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOptional() {
    return optional;
  }


  @JsonProperty(JSON_PROPERTY_OPTIONAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOptional(@javax.annotation.Nullable Boolean optional) {
    this.optional = optional;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    V1ConfigMapProjection v1ConfigMapProjection = (V1ConfigMapProjection) o;
    return Objects.equals(this.items, v1ConfigMapProjection.items) &&
        Objects.equals(this.name, v1ConfigMapProjection.name) &&
        Objects.equals(this.optional, v1ConfigMapProjection.optional);
  }

  @Override
  public int hashCode() {
    return Objects.hash(items, name, optional);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class V1ConfigMapProjection {\n");
    sb.append("    items: ").append(toIndentedString(items)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    optional: ").append(toIndentedString(optional)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

