/*
 * KS API
 * KubeSphere OpenAPI
 *
 * The version of the OpenAPI document: v4.1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package cn.sh.ideal.ccp.lib.kubeSphere.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ReplicaSet ensures that a specified number of pod replicas are running at any given time.
 */
@JsonPropertyOrder({
  V1ReplicaSet.JSON_PROPERTY_API_VERSION,
  V1ReplicaSet.JSON_PROPERTY_KIND,
  V1ReplicaSet.JSON_PROPERTY_METADATA,
  V1ReplicaSet.JSON_PROPERTY_SPEC,
  V1ReplicaSet.JSON_PROPERTY_STATUS
})
@JsonTypeName("v1.ReplicaSet")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-07T14:59:55.982671800+08:00[GMT+08:00]", comments = "Generator version: 7.13.0")
public class V1ReplicaSet {
  public static final String JSON_PROPERTY_API_VERSION = "apiVersion";
  @javax.annotation.Nullable
  private String apiVersion;

  public static final String JSON_PROPERTY_KIND = "kind";
  @javax.annotation.Nullable
  private String kind;

  public static final String JSON_PROPERTY_METADATA = "metadata";
  @javax.annotation.Nullable
  private V1ObjectMeta metadata;

  public static final String JSON_PROPERTY_SPEC = "spec";
  @javax.annotation.Nullable
  private V1ReplicaSetSpec spec;

  public static final String JSON_PROPERTY_STATUS = "status";
  @javax.annotation.Nullable
  private V1ReplicaSetStatus status;

  public V1ReplicaSet() {
  }

  public V1ReplicaSet apiVersion(@javax.annotation.Nullable String apiVersion) {

    this.apiVersion = apiVersion;
    return this;
  }

  /**
   * APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
   * @return apiVersion
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_API_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getApiVersion() {
    return apiVersion;
  }


  @JsonProperty(JSON_PROPERTY_API_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setApiVersion(@javax.annotation.Nullable String apiVersion) {
    this.apiVersion = apiVersion;
  }

  public V1ReplicaSet kind(@javax.annotation.Nullable String kind) {

    this.kind = kind;
    return this;
  }

  /**
   * Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
   * @return kind
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KIND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getKind() {
    return kind;
  }


  @JsonProperty(JSON_PROPERTY_KIND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKind(@javax.annotation.Nullable String kind) {
    this.kind = kind;
  }

  public V1ReplicaSet metadata(@javax.annotation.Nullable V1ObjectMeta metadata) {

    this.metadata = metadata;
    return this;
  }

  /**
   * Get metadata
   * @return metadata
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V1ObjectMeta getMetadata() {
    return metadata;
  }


  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMetadata(@javax.annotation.Nullable V1ObjectMeta metadata) {
    this.metadata = metadata;
  }

  public V1ReplicaSet spec(@javax.annotation.Nullable V1ReplicaSetSpec spec) {

    this.spec = spec;
    return this;
  }

  /**
   * Get spec
   * @return spec
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SPEC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V1ReplicaSetSpec getSpec() {
    return spec;
  }


  @JsonProperty(JSON_PROPERTY_SPEC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSpec(@javax.annotation.Nullable V1ReplicaSetSpec spec) {
    this.spec = spec;
  }

  public V1ReplicaSet status(@javax.annotation.Nullable V1ReplicaSetStatus status) {

    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V1ReplicaSetStatus getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(@javax.annotation.Nullable V1ReplicaSetStatus status) {
    this.status = status;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    V1ReplicaSet v1ReplicaSet = (V1ReplicaSet) o;
    return Objects.equals(this.apiVersion, v1ReplicaSet.apiVersion) &&
        Objects.equals(this.kind, v1ReplicaSet.kind) &&
        Objects.equals(this.metadata, v1ReplicaSet.metadata) &&
        Objects.equals(this.spec, v1ReplicaSet.spec) &&
        Objects.equals(this.status, v1ReplicaSet.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(apiVersion, kind, metadata, spec, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class V1ReplicaSet {\n");
    sb.append("    apiVersion: ").append(toIndentedString(apiVersion)).append("\n");
    sb.append("    kind: ").append(toIndentedString(kind)).append("\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    spec: ").append(toIndentedString(spec)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

