package cn.sh.ideal.ccp.spec.model;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;


/**
 * <AUTHOR>
 */
public interface ComputingArchitectureRepository extends JpaRepository<ComputingArchitectureDO, Long>,
  JpaSpecificationExecutor<ComputingArchitectureDO> {




  Optional<ComputingArchitectureDO> findByCode(@NotNull String code);

}
