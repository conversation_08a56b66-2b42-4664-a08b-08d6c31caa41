package cn.sh.ideal.ccp.container.model;

import cn.sh.ideal.ccp.api.spec.SpecFlavorsApi;
import cn.sh.ideal.ccp.api.storage.StorageInstanceV2Api;
import cn.sh.ideal.ccp.container.build.KubeSphereClient;
import cn.sh.ideal.ccp.container.configure.ContainerProperties;
import cn.sh.ideal.ccp.lib.kubeSphere.model.V2ApplicationList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class KubeSphereAppsApi {

  private final KubeSphereClient kubeSphereClient;
  private final ContainerProperties properties;


  /**
   * 查看应用商店应用列表
   */
  public V2ApplicationList queryApps() {
    return kubeSphereClient.getDefaultApi()
      .listAppsWithResponseSpec()
      .bodyToMono(V2ApplicationList.class)
      .block();
  }


  /**
   * 查询应用分类
   */






}
