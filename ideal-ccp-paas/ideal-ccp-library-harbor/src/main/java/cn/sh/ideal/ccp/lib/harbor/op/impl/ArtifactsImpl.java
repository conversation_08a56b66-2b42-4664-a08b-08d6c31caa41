package cn.sh.ideal.ccp.lib.harbor.op.impl;


import cn.sh.ideal.ccp.lib.harbor.HarborResponse;
import cn.sh.ideal.ccp.lib.harbor.model.Artifact;
import cn.sh.ideal.ccp.lib.harbor.model.ArtifactListFilter;
import cn.sh.ideal.ccp.lib.harbor.op.Artifacts;
import cn.sh.ideal.ccp.lib.harbor.op.handler.ArtifactHandler;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
class ArtifactsImpl implements Artifacts {

  private final String repositoryBaseApi;
  private final String repositoryName;
  private final HarborApiImplV2 client;

  ArtifactsImpl(HarborApiImplV2 client, String repositoryBaseApi, String repositoryName) {
    this.client = client;
    this.repositoryBaseApi = repositoryBaseApi;
    this.repositoryName = repositoryName;
  }

  @Override
  public List<Artifact> list() {
    return list(null);
  }

  @Override
  public List<Artifact> list(ArtifactListFilter filter) {
    String artifactBaseApi = getArtifactBaseApi();
    if (filter == null) {
      filter = ArtifactListFilter.DEFAULT;
    }
    int page = filter.getPage();
    int pageSize = filter.getPageSize();
    String query = filter.getQuery();
    boolean withTag = filter.isWithTag();
    boolean withLabel = filter.isWithLabel();
    boolean withScanOverview = filter.isWithScanOverview();
    artifactBaseApi += "?page=" + page + "&page_size=" + pageSize;
    if (StringUtils.isNotEmpty(query)) {
      artifactBaseApi += "&q=" + query;
    }
    if (withTag) {
      artifactBaseApi += "&with_tag=true";
    }
    if (withLabel) {
      artifactBaseApi += "&with_label=true";
    }
    if (withScanOverview) {
      artifactBaseApi += "&with_scan_overview=true";
    }
    return client.list(artifactBaseApi, Artifact.class);
  }

  @Override
  public ArtifactHandler withReference(String reference) {
    return new ArtifactHandlerImpl(getArtifactBaseApi(), reference, client);
  }

  @Override
  public Integer count() {
    return client.count(getArtifactBaseApi());
  }

  @Override
  public HarborResponse copy(String from) {
    Objects.requireNonNull(from, "from can not be null");
    String artifactBaseApi = getArtifactBaseApi();
    if (StringUtils.isNotEmpty(from)) {
      artifactBaseApi += "?from=" + from;
    }
    return client.post(artifactBaseApi);
  }

  private String getArtifactBaseApi() {
    return repositoryBaseApi + "/" + URLEncoder.encode(repositoryName, StandardCharsets.UTF_8) + "/artifacts";
  }

}
